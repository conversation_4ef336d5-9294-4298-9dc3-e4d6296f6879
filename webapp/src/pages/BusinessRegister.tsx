import { useState, useContext, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import { API_URL } from '../config';
import { loadGoogleMapsApi } from '../utils/googleMapsLoader';
import RegistrationSuccess from '../components/RegistrationSuccess';
import LocationAutocomplete from '../components/LocationAutocomplete';
import '../types/google-maps';

const BusinessRegister = () => {
  const navigate = useNavigate();
  const { loading: authLoading, error: authError, clearError } = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  // No need for autocomplete refs as we're using the LocationAutocomplete component
  const [isLoadingMapsApi, setIsLoadingMapsApi] = useState(true);


  // Load Google Maps API with Places library
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['places']);
        setIsLoadingMapsApi(false);
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setIsLoadingMapsApi(false);
      }
    };

    loadMapsApi();
  }, []);

  // No need to initialize Google Places Autocomplete Element separately
  // as we're using the LocationAutocomplete component

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    companyName: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'USA',
    businessType: 'supplier', // or 'vet'
    productCategories: [] as string[],
    serviceAreas: [] as string[],
    description: '',
    website: '',
    acceptTerms: false
  });

  const [passwordError, setPasswordError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle address selection from LocationAutocomplete
  const handleAddressSelect = (address: string, coordinates?: { lat: number; lng: number }, addressComponents?: any) => {
    // If we have address components directly from the autocomplete
    if (addressComponents) {
      console.log('Address components received:', addressComponents);

      // Extract address components, with fallbacks
      const streetAddress = addressComponents.streetAddress || '';
      const city = addressComponents.city || '';
      const state = addressComponents.state || '';
      const zipCode = addressComponents.zipCode || '';
      const country = addressComponents.country || 'USA';

      // Use streetAddress if available, otherwise use the full address
      const addressToUse = streetAddress || address;

      console.log('Extracted components:', { addressToUse, city, state, zipCode, country });

      // Update form data with extracted components
      setFormData(prev => ({
        ...prev,
        address: addressToUse,
        city: city || prev.city,
        state: state || prev.state,
        zipCode: zipCode || prev.zipCode,
        country: country || prev.country
      }));
    }
    // If we have coordinates but no address components, use geocoder as fallback
    else if (coordinates && window.google && window.google.maps) {
      const geocoder = new google.maps.Geocoder();

      geocoder.geocode({ location: coordinates }, (results: google.maps.GeocoderResult[], status: google.maps.GeocoderStatus) => {
        if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
          const place = results[0];

          // Extract address components
          let streetNumber = '';
          let street = '';
          let streetAddress = '';
          let city = '';
          let state = '';
          let zipCode = '';
          let country = 'USA';

          if (place.address_components) {
            // First pass: extract primary components
            for (const component of place.address_components) {
              const componentType = component.types[0];

              switch (componentType) {
                case 'street_number':
                  streetNumber = component.long_name;
                  break;
                case 'route':
                  street = component.long_name;
                  break;
                case 'locality':
                  city = component.long_name;
                  break;
                case 'administrative_area_level_1':
                  state = component.short_name;
                  break;
                case 'postal_code':
                  zipCode = component.long_name;
                  break;
                case 'country':
                  country = component.long_name;
                  break;
              }
            }

            // Construct street address from street number and street
            if (streetNumber && street) {
              streetAddress = `${streetNumber} ${street}`;
            }

            // Second pass: check for alternative component types if primary ones are missing
            if (!city) {
              const sublocality = place.address_components.find(comp =>
                comp.types.includes('sublocality') || comp.types.includes('sublocality_level_1'));
              if (sublocality) {
                city = sublocality.long_name;
              }
            }

            if (!city) {
              const neighborhood = place.address_components.find(comp => comp.types.includes('neighborhood'));
              if (neighborhood) {
                city = neighborhood.long_name;
              }
            }

            if (!state) {
              const adminArea2 = place.address_components.find(comp =>
                comp.types.includes('administrative_area_level_2'));
              if (adminArea2) {
                state = adminArea2.short_name;
              }
            }
          }

          // Use streetAddress if available, otherwise use the full address
          const addressToUse = streetAddress || address;

          // Update form data with extracted components
          setFormData(prev => ({
            ...prev,
            address: addressToUse,
            city: city || prev.city,
            state: state || prev.state,
            zipCode: zipCode || prev.zipCode,
            country: country || prev.country
          }));
        }
      });
    }
    // If we have neither address components nor coordinates, just update the address field
    // This happens when the user types an address manually
    else {
      console.log('Manual address input:', address);
      setFormData(prev => ({
        ...prev,
        address: address
      }));
    }
  };
  const validateForm = () => {
    clearError();
    setPasswordError(null);

    // Validate email
    if (!formData.email || !/^\S+@\S+\.\S+$/.test(formData.email)) {
      setPasswordError('Please enter a valid email address');
      return false;
    }

    // Validate name fields
    if (!formData.firstName.trim()) {
      setPasswordError('First name is required');
      return false;
    }

    if (!formData.lastName.trim()) {
      setPasswordError('Last name is required');
      return false;
    }

    // Validate password
    if (formData.password !== formData.confirmPassword) {
      setPasswordError('Passwords do not match');
      return false;
    }

    if (formData.password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return false;
    }

    // Validate required fields
    if (!formData.phoneNumber) {
      setPasswordError('Phone number is required');
      return false;
    }

    if (!formData.companyName) {
      setPasswordError('Business name is required');
      return false;
    }

    if (!formData.address) {
      setPasswordError('Address is required');
      return false;
    }

    if (!formData.city) {
      setPasswordError('City is required');
      return false;
    }

    if (!formData.state) {
      setPasswordError('State is required');
      return false;
    }

    if (!formData.zipCode) {
      setPasswordError('Zip code is required');
      return false;
    }

    // Validate terms
    if (!formData.acceptTerms) {
      setPasswordError('You must accept the terms and conditions');
      return false;
    }

    // Validate product categories for suppliers
    if (formData.businessType === 'supplier' && formData.productCategories.length === 0) {
      setPasswordError('Please select at least one product category');
      return false;
    }

    // Validate service areas
    if (formData.serviceAreas.length === 0) {
      setPasswordError('Please select at least one service area');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Prepare the data for submission
      const businessData = {
        email: formData.email.trim(),
        password: formData.password,
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        userType: formData.businessType,
        businessDetails: {
          name: formData.companyName.trim(),
          address: formData.address.trim(),
          city: formData.city.trim(),
          state: formData.state.trim(),
          zipCode: formData.zipCode.trim(),
          country: formData.country,
          productCategories: formData.productCategories,
          serviceAreas: formData.serviceAreas,
          description: formData.description.trim(),
          website: formData.website.trim()
        }
      };

      // Register the user with business details using the register-business endpoint
      const response = await axios.post(`${API_URL}/auth/register-business`, businessData);

      console.log('Registration successful:', response.data);

      // Set registration success to show verification prompt
      setRegistrationSuccess(true);
    } catch (err: any) {
      console.error('Registration error:', err);
      if (err.response && err.response.data && err.response.data.error) {
        setError(err.response.data.error);
      } else {
        setError('An error occurred during registration. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (registrationSuccess) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-card">
          <RegistrationSuccess />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-card">
        <div className="flex flex-col items-center">
          <img
            src="/logo.svg"
            alt="nxtAcre Logo"
            className="h-12 w-auto mb-4"
            onError={(e) => {
              // Fallback if logo doesn't exist
              const target = e.currentTarget;
              target.onerror = null;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                const span = document.createElement('span');
                span.className = 'text-3xl font-display font-bold text-primary-600';
                span.textContent = 'nxtAcre';
                parent.appendChild(span);
              }
            }}
          />
          <h2 className="text-center text-2xl font-display font-bold text-gray-900">
            Create your business account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Register your business to manage your listing
          </p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {passwordError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
            <span className="block sm:inline">{passwordError}</span>
          </div>
        )}

        <form className="mt-6 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="mt-4 pt-4 border-t border-gray-200">
              <h3 className="text-md font-medium text-gray-700 mb-2">Business Type</h3>
              <p className="text-sm text-gray-600 mb-3">
                Select your business type to manage your listing within the system.
              </p>

              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                      id="business-supplier"
                      name="business-type"
                      type="radio"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                      checked={formData.businessType === 'supplier'}
                      onChange={() => {
                        setFormData(prev => ({ ...prev, businessType: 'supplier' }));
                      }}
                  />
                  <label htmlFor="business-supplier" className="ml-3 block text-sm font-medium text-gray-700">
                    Supplier / Vendor
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                      id="business-vet"
                      name="business-type"
                      type="radio"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                      checked={formData.businessType === 'vet'}
                      onChange={() => {
                        setFormData(prev => ({ ...prev, businessType: 'vet' }));
                      }}
                  />
                  <label htmlFor="business-vet" className="ml-3 block text-sm font-medium text-gray-700">
                    Veterinarian
                  </label>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="first-name" className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                <input
                  id="first-name"
                  name="firstName"
                  type="text"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="John"
                  value={formData.firstName}
                  onChange={handleChange}
                />
              </div>
              <div>
                <label htmlFor="last-name" className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <input
                  id="last-name"
                  name="lastName"
                  type="text"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="Doe"
                  value={formData.lastName}
                  onChange={handleChange}
                />
              </div>
            </div>
            <div>
              <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">Email address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="phone-number" className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
              <input
                id="phone-number"
                name="phoneNumber"
                type="tel"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="(*************"
                value={formData.phoneNumber}
                onChange={handleChange}
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="••••••••"
                value={formData.password}
                onChange={handleChange}
              />
              <p className="mt-1 text-xs text-gray-500">Must be at least 8 characters long</p>
            </div>
            <div>
              <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
              <input
                id="confirm-password"
                name="confirmPassword"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="••••••••"
                value={formData.confirmPassword}
                onChange={handleChange}
              />
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <h3 className="text-md font-medium text-gray-700 mb-2">Business Information</h3>
              <p className="text-sm text-gray-600 mb-3">
                Enter your business details to complete your registration.
              </p>

              <div className="space-y-4">
                <div>
                  <label htmlFor="company-name" className="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                  <input
                    id="company-name"
                    name="companyName"
                    type="text"
                    required
                    className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                    placeholder="Your Business Name"
                    value={formData.companyName}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">Street Address</label>
                  {!isLoadingMapsApi ? (
                    <LocationAutocomplete
                      value={formData.address}
                      onChange={handleAddressSelect}
                      placeholder="123 Main St"
                    />
                  ) : (
                    <input
                      id="address"
                      name="address"
                      type="text"
                      required
                      className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                      placeholder="Loading street address autocomplete..."
                      value={formData.address}
                      onChange={handleChange}
                    />
                  )}
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">City</label>
                    <input
                      id="city"
                      name="city"
                      type="text"
                      required
                      autoComplete="off"
                      className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                      placeholder="City"
                      value={formData.city}
                      onChange={handleChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">State</label>
                    <select
                      id="state"
                      name="state"
                      required
                      autoComplete="off"
                      className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                      value={formData.state}
                      onChange={handleChange}
                    >
                      <option value="">Select a state</option>
                      <option value="AL">Alabama</option>
                      <option value="AK">Alaska</option>
                      <option value="AZ">Arizona</option>
                      <option value="AR">Arkansas</option>
                      <option value="CA">California</option>
                      <option value="CO">Colorado</option>
                      <option value="CT">Connecticut</option>
                      <option value="DE">Delaware</option>
                      <option value="DC">District Of Columbia</option>
                      <option value="FL">Florida</option>
                      <option value="GA">Georgia</option>
                      <option value="HI">Hawaii</option>
                      <option value="ID">Idaho</option>
                      <option value="IL">Illinois</option>
                      <option value="IN">Indiana</option>
                      <option value="IA">Iowa</option>
                      <option value="KS">Kansas</option>
                      <option value="KY">Kentucky</option>
                      <option value="LA">Louisiana</option>
                      <option value="ME">Maine</option>
                      <option value="MD">Maryland</option>
                      <option value="MA">Massachusetts</option>
                      <option value="MI">Michigan</option>
                      <option value="MN">Minnesota</option>
                      <option value="MS">Mississippi</option>
                      <option value="MO">Missouri</option>
                      <option value="MT">Montana</option>
                      <option value="NE">Nebraska</option>
                      <option value="NV">Nevada</option>
                      <option value="NH">New Hampshire</option>
                      <option value="NJ">New Jersey</option>
                      <option value="NM">New Mexico</option>
                      <option value="NY">New York</option>
                      <option value="NC">North Carolina</option>
                      <option value="ND">North Dakota</option>
                      <option value="OH">Ohio</option>
                      <option value="OK">Oklahoma</option>
                      <option value="OR">Oregon</option>
                      <option value="PA">Pennsylvania</option>
                      <option value="RI">Rhode Island</option>
                      <option value="SC">South Carolina</option>
                      <option value="SD">South Dakota</option>
                      <option value="TN">Tennessee</option>
                      <option value="TX">Texas</option>
                      <option value="UT">Utah</option>
                      <option value="VT">Vermont</option>
                      <option value="VA">Virginia</option>
                      <option value="WA">Washington</option>
                      <option value="WV">West Virginia</option>
                      <option value="WI">Wisconsin</option>
                      <option value="WY">Wyoming</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-1">Zip Code</label>
                    <input
                      id="zipCode"
                      name="zipCode"
                      type="text"
                      required
                      autoComplete="off"
                      className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                      placeholder="12345"
                      value={formData.zipCode}
                      onChange={handleChange}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">Website (optional)</label>
                  <input
                    id="website"
                    name="website"
                    type="url"
                    className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                    placeholder="https://www.yourbusiness.com"
                    value={formData.website}
                    onChange={handleChange}
                  />
                </div>

                {formData.businessType === 'supplier' && (
                  <div>
                    <label htmlFor="productCategories" className="block text-sm font-medium text-gray-700 mb-1">Product Categories</label>
                    <p className="text-xs text-gray-500 mb-2">Select the categories of products you supply</p>
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        'Seeds', 'Fertilizers', 'Pesticides', 'Herbicides', 'Fungicides',
                        'Equipment', 'Tools', 'Irrigation', 'Livestock Feed', 'Animal Health',
                        'Soil Amendments', 'Organic Products', 'Farm Supplies', 'Other'
                      ].map((category) => (
                        <div key={category} className="flex items-center">
                          <input
                            id={`category-${category}`}
                            type="checkbox"
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            checked={formData.productCategories.includes(category)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFormData(prev => ({
                                  ...prev,
                                  productCategories: [...prev.productCategories, category]
                                }));
                              } else {
                                setFormData(prev => ({
                                  ...prev,
                                  productCategories: prev.productCategories.filter(c => c !== category)
                                }));
                              }
                            }}
                          />
                          <label htmlFor={`category-${category}`} className="ml-2 block text-sm text-gray-700">
                            {category}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <label htmlFor="serviceAreas" className="block text-sm font-medium text-gray-700 mb-1">Service Areas</label>
                  <p className="text-xs text-gray-500 mb-2">Select the areas where you provide services</p>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      'Northeast', 'Southeast', 'Midwest', 'Southwest', 'West',
                      'Northwest', 'National', 'International'
                    ].map((area) => (
                      <div key={area} className="flex items-center">
                        <input
                          id={`area-${area}`}
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={formData.serviceAreas.includes(area)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData(prev => ({
                                ...prev,
                                serviceAreas: [...prev.serviceAreas, area]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                serviceAreas: prev.serviceAreas.filter(a => a !== area)
                              }));
                            }
                          }}
                        />
                        <label htmlFor={`area-${area}`} className="ml-2 block text-sm text-gray-700">
                          {area}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">Business Description (optional)</label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                    placeholder="Describe your business and services"
                    value={formData.description}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>

            <div className="mt-4">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="terms"
                    name="acceptTerms"
                    type="checkbox"
                    checked={formData.acceptTerms}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="terms" className="font-medium text-gray-700">
                    I accept the{' '}
                    <a href="https://www.nxtacre.com/terms-and-conditions" target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-700">
                      Terms and Conditions
                    </a>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className={`btn btn-primary w-full ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating account...
                </span>
              ) : 'Create business account'}
            </button>
          </div>
          <div className="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
            <p>Business accounts require approval from administrators before they can manage their listings.</p>
            <p className="mt-1">You can still use the platform while your business account is pending approval.</p>
          </div>

          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-4">
              Already have an account?{' '}
              <Link to="/login" className="font-medium text-primary-600 hover:text-primary-700">
                Sign in
              </Link>
            </p>
            <p className="text-sm text-gray-600">
              Need a farmer account instead?{' '}
              <Link to="/register" className="font-medium text-primary-600 hover:text-primary-700">
                Register as a farmer
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BusinessRegister;
